/* Import browser normalization */
@import "../styles/browser-normalize.css";

/* Import component-specific styles */
@import "../styles/post-creation.css";
@import "../styles/chat-modal.css";
@import "../styles/modal-responsive.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom navigation tab styles */
@layer utilities {
  .border-b-3 {
    border-bottom-width: 3px;
  }

  /* Community content container utility classes */
  .community-content-wrapper {
    @apply w-full flex justify-center items-center;
  }

  .community-content-container {
    @apply w-full md:w-2/3;
  }
}

/* Razorpay Modal Fixes */
.razorpay-container {
  z-index: 999999 !important;
}

.razorpay-backdrop {
  z-index: 999998 !important;
}

/* Ensure Razorpay iframe is visible when modal is open */
iframe.razorpay-checkout-frame {
  z-index: 999999 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure Razorpay elements have proper z-index */
[class*="razorpay"] {
  z-index: 999999 !important;
}

body {
  font-family: var(--font-poppins), Arial, Helvetica, sans-serif;
}

body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Force theme colors on cards */
.card {
  background-color: var(--card-bg) !important;
  color: var(--text-primary) !important;
  border-color: var(--card-border) !important;
}

/* Force theme colors on community feed */
.community-feed .card,
.post-card {
  background-color: var(--card-bg) !important;
  color: var(--text-primary) !important;
  border-color: var(--card-border) !important;
}

.tabs {
  align-items: start;
}

/* Ensure consistent layout across browsers */
.container {
  width: 66.6667%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Fix for card overlapping issues */
@layer components {
  .spotlight-card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
  }
  
  @media (max-width: 768px) {
    .spotlight-card-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }
  
  @media (min-width: 769px) and (max-width: 1024px) {
    .spotlight-card-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (min-width: 1025px) {
    .spotlight-card-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

/* Fix for inconsistent form elements */
input,
textarea,
select,
button {
  -webkit-appearance: none;
  appearance: none;
  border-radius: 0.5rem;
  font-family: inherit;
}

/* Typing indicator animation */
.typing-indicator {
  display: flex;
  align-items: center;
  column-gap: 6px;
  padding: 4px 8px;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  background-color: #888;
  border-radius: 50%;
  display: block;
  opacity: 0.4;
}

.typing-indicator span:nth-child(1) {
  animation: typing 1s infinite;
}

.typing-indicator span:nth-child(2) {
  animation: typing 1s 0.33s infinite;
}

.typing-indicator span:nth-child(3) {
  animation: typing 1s 0.66s infinite;
}

@keyframes typing {
  0%,
  100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-5px);
    opacity: 0.8;
  }
}

/* Mobile responsiveness for message modal */
@media (max-width: 640px) {
  .message-modal {
    width: 100%;
    height: 100%;
    max-width: none;
    border-radius: 0;
  }
}

/* Calendar Styles */
.fc {
  --fc-border-color: #e2e8f0;
  --fc-button-bg-color: #3788d8;
  --fc-button-border-color: #3788d8;
  --fc-button-hover-bg-color: #2c6cb0;
  --fc-button-hover-border-color: #2c6cb0;
  --fc-button-active-bg-color: #1a56db;
  --fc-button-active-border-color: #1a56db;
  --fc-event-bg-color: #3788d8;
  --fc-event-border-color: #3788d8;
  --fc-today-bg-color: rgba(255, 220, 40, 0.15);
}

.fc .fc-daygrid-day.fc-day-today {
  background-color: var(--fc-today-bg-color);
}

.fc .fc-button {
  font-weight: 500;
}

.fc .fc-event {
  cursor: pointer;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.2s ease;
}

.fc .fc-event:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.fc-theme-standard .fc-scrollgrid {
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  overflow: hidden;
}

.fc-theme-standard td,
.fc-theme-standard th {
  border: 1px solid #e2e8f0;
}

.fc .fc-daygrid-day-number {
  padding: 0.5rem;
}

.fc .fc-col-header-cell-cushion {
  padding: 0.5rem;
  font-weight: 500;
}

/* Make calendar responsive */
@media (max-width: 640px) {
  .fc .fc-toolbar {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .fc .fc-toolbar-title {
    font-size: 1.125rem;
  }
}

/* Custom styles for the calendar container */
.calendar-container {
  width: 100%;
  overflow-x: auto;
  min-height: 500px;
}

/* Ensure the calendar fits on mobile screens */
@media (max-width: 640px) {
  .calendar-container {
    min-height: 400px;
  }
}

/* Removed shadcn/ui variable overrides to avoid conflicts with DaisyUI themes */
/*
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
*/
