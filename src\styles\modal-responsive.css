/* Modal Responsive Styles */

/* Hide scrollbars but keep functionality */
.modal-content-scroll {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.modal-content-scroll::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

/* Custom scrollbar for when needed */
.modal-custom-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

.modal-custom-scroll::-webkit-scrollbar {
  width: 6px;
}

.modal-custom-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.modal-custom-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
}

.modal-custom-scroll::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}

/* Ensure proper mobile spacing */
@media (max-width: 640px) {
  .modal-mobile-padding {
    padding: 0.75rem;
  }

  .modal-mobile-spacing > * + * {
    margin-top: 0.75rem;
  }
}
