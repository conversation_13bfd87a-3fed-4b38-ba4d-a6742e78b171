/* Browser compatibility fixes */

/* Fix for Firefox */
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/* Fix for Safari and Chrome */
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/* Fix for Edge */
input::-ms-clear,
input::-ms-reveal {
  display: none;
  width: 0;
  height: 0;
}

/* Fix for IE */
input::-ms-clear,
input::-ms-reveal {
  display: none;
  width: 0;
  height: 0;
}
