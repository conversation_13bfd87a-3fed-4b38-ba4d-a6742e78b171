/* Chat modal specific styles */
.chat-modal .avatar,
.chat-modal .avatar > div,
.chat-modal .avatar img,
.message-modal .avatar,
.message-modal .avatar > div,
.message-modal .avatar img,
.dropdown-content .avatar,
.dropdown-content .avatar > div,
.dropdown-content .avatar img {
  border-radius: 50% !important;
  overflow: hidden !important;
}

/* Override any border styles that might be affecting the avatar */
.message-modal .avatar {
  border: none !important;
  box-shadow: none !important;
}

/* Ensure images inside avatars are properly rounded */
.avatar img,
.avatar > div > img,
.chat-avatar-img {
  border-radius: 50% !important;
  object-fit: cover;
  width: 100%;
  height: 100%;
}

/* Specific style for chat avatar images */
.chat-avatar-img {
  border-radius: 50% !important;
  overflow: hidden !important;
  display: block;
}

/* Ensure fallback avatars are properly rounded */
.avatar > div > div {
  border-radius: 50% !important;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
