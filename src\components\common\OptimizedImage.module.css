.imageContainer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden; /* Prevent image overflow */
  background-color: #f1f1f1; /* Light gray placeholder */
  will-change: transform; /* Optimize for animations */
}

.loading {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(249, 250, 251, 0.5);
  z-index: 1;
}

.image {
  width: 100%;
  height: 100%;
  transition: opacity 0.2s ease-in-out;
  opacity: 1;
  object-fit: cover; /* Default object-fit */
  transform: translateZ(0); /* Force GPU acceleration */
}

.imageCover {
  object-fit: cover;
}

.imageContain {
  object-fit: contain;
}

.imageFill {
  object-fit: fill;
}

/* For Google profile images */
img.image {
  display: block;
  object-position: center;
}

.fallbackContainer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  color: #9ca3af;
  font-weight: 500;
}
