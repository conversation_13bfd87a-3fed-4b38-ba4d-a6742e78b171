/* Modern Theme Variables CSS */

/* Common variables for both themes */
:root {
  /* Brand Colors - Modern and Professional */
  --brand-primary: #fcaa96;
  --brand-secondary: #8f2c2c;
  --brand-accent: #06b6d4;
  --brand-success: #10b981;
  --brand-warning: #f59e0b;
  --brand-error: #ef4444;

  /* Theme transition */
  --theme-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Light Theme */
[data-theme="light"] {
  /* Background colors */
  --bg-primary: #faf7f4;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-accent: #e2e8f0;

  /* Text colors */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-muted: #94a3b8;

  /* Border colors */
  --border-color: #e2e8f0;
  --border-hover: #cbd5e1;
  --border-focus: var(--brand-primary);

  /* Shadow */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Component specific */
  --card-bg: #ffffff;
  --card-border: #e2e8f0;
  --input-bg: #ffffff;
  --input-border: #d1d5db;
  --input-focus: var(--brand-primary);
  --button-hover-overlay: rgba(0, 0, 0, 0.05);
  --dropdown-bg: #ffffff;
  --modal-bg: #ffffff;
  --modal-overlay: rgba(0, 0, 0, 0.5);
  --tooltip-bg: #1f2937;
  --tooltip-text: #ffffff;

  /* Interactive states */
  --hover-bg: #f8fafc;
  --active-bg: #f1f5f9;
  --focus-ring: 0 0 0 3px rgba(59, 130, 246, 0.1);

  /* Chart colors */
  --chart-primary: var(--brand-primary);
  --chart-secondary: var(--brand-secondary);
  --chart-grid: #e2e8f0;
  --chart-text: #64748b;
}

/* Dark Theme */
[data-theme="dark"] {
  /* Background colors */
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1e24;
  --bg-tertiary: #1f2226;
  --bg-accent: #475569;

  /* Text colors */
  --text-primary: #feffe2;
  --text-secondary: #e2e8f0;
  --text-tertiary: #cbd5e1;
  --text-muted: #94a3b8;

  /* Border colors */
  --border-color: #334155;
  --border-hover: #475569;
  --border-focus: var(--brand-primary);

  /* Shadow */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.5);

  /* Component specific */
  --card-bg: #1a1e24;
  --card-border: #334155;
  --input-bg: #1e293b;
  --input-border: #475569;
  --input-focus: var(--brand-primary);
  --button-hover-overlay: rgba(255, 255, 255, 0.1);
  --dropdown-bg: #191918;
  --modal-bg: #191918;
  --modal-overlay: rgba(0, 0, 0, 0.8);
  --tooltip-bg: #374151;
  --tooltip-text: #f9fafb;

  /* Interactive states */
  --hover-bg: #292929;
  --active-bg: #475569;
  --focus-ring: 0 0 0 3px rgba(30, 30, 30, 0.2);

  /* Chart colors */
  --chart-primary: var(--brand-primary);
  --chart-secondary: var(--brand-secondary);
  --chart-grid: #475569;
  --chart-text: #94a3b8;
}
